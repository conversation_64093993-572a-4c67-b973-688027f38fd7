<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limitless Settings - Web Test</title>
    <style>
        /* Copy the styles from the original settings.html */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .settings-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .settings-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected { background: #28a745; }
        .status-disconnected { background: #dc3545; }
        .status-pending { background: #ffc107; }
        
        #js-loaded-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: green;
            color: white;
            padding: 5px;
            z-index: 9999;
            font-size: 12px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <div class="settings-header">
            <h1>🧠 Limitless AI Configuration - Web Test</h1>
            <p>Test the settings functionality in a web browser</p>
        </div>

        <div class="settings-content">
            <section class="settings-section">
                <h2>API Configuration</h2>

                <div class="form-group">
                    <label for="apiKey">
                        <span class="label-text">Limitless AI API Key</span>
                        <span class="label-hint">Your personal API key from Limitless AI Developer settings</span>
                    </label>
                    <input 
                        type="password" 
                        id="apiKey" 
                        placeholder="Enter your Limitless AI API key"
                        autocomplete="off"
                    >
                    <div class="form-hint">
                        Keep this secure. Your API key is stored locally and never shared.
                    </div>
                </div>

                <div class="form-group">
                    <button type="button" id="validateBtn">Validate API Key</button>
                    <div id="validationStatus"></div>
                </div>
            </section>

            <section class="settings-section">
                <h2>Synchronization Settings</h2>

                <div class="form-group">
                    <label for="autoSync">
                        <input type="checkbox" id="autoSync" checked>
                        <span class="checkbox-label">Enable automatic synchronization</span>
                    </label>
                    <div class="form-hint">Automatically fetch new lifelogs at regular intervals</div>
                </div>

                <div class="form-group">
                    <label for="syncInterval">
                        <span class="label-text">Sync Interval</span>
                        <span class="label-hint">How often to check for new data (in hours)</span>
                    </label>
                    <select id="syncInterval">
                        <option value="1">Every hour</option>
                        <option value="2">Every 2 hours</option>
                        <option value="4">Every 4 hours</option>
                        <option value="6" selected>Every 6 hours</option>
                        <option value="12">Every 12 hours</option>
                        <option value="24">Daily</option>
                    </select>
                </div>
            </section>

            <section class="settings-section">
                <div class="settings-actions">
                    <button type="button" id="saveBtn">Save Settings</button>
                    <button type="button" id="resetBtn" class="btn-secondary">Reset to Defaults</button>
                </div>
            </section>
        </div>
    </div>

    <!-- Mock the plugin API for web testing -->
    <script>
        // Mock plugin API for web testing
        window.pluginAPI = {
            storage: {
                loadData: () => ({}),
                saveData: (data) => {
                    console.log('Mock storage save:', data);
                    return true;
                }
            },
            getLogger: () => ({
                info: console.log,
                warn: console.warn,
                error: console.error,
                debug: console.log
            })
        };
        
        // Mock lifeboard API for web testing
        window.lifeboard = {
            settings: {
                save: async (pluginId, settings, schema, options) => {
                    console.log('Mock lifeboard.settings.save called:', {
                        pluginId, settings, schema, options
                    });
                    
                    // Simulate validation
                    if (options?.validateOnly) {
                        if (!settings.apiKey || settings.apiKey.trim().length === 0) {
                            return { success: false, error: 'API not valid' };
                        }
                        return { success: true };
                    }
                    
                    // Simulate save
                    return true;
                }
            }
        };
        
        // Mock require for web context
        window.require = (module) => {
            if (module === './logger') {
                return class MockLogger {
                    constructor() {}
                    async info(msg, data) { console.log('Logger INFO:', msg, data); }
                    async warn(msg, data) { console.warn('Logger WARN:', msg, data); }
                    async error(msg, data) { console.error('Logger ERROR:', msg, data); }
                    async debug(msg, data) { console.log('Logger DEBUG:', msg, data); }
                };
            }
            if (module === 'fs') {
                return {
                    appendFileSync: (file, data) => console.log('Mock fs.appendFileSync:', file, data)
                };
            }
            if (module === 'path') {
                return {
                    join: (...args) => args.join('/')
                };
            }
            return {};
        };
    </script>

    <!-- Simplified settings logic for web testing -->
    <script>
        console.log('🚨🚨🚨 CRITICAL: Web test JavaScript loaded at', new Date().toISOString());

        // Add visible indicator that JavaScript is loaded
        const indicator = document.createElement('div');
        indicator.id = 'js-loaded-indicator';
        indicator.textContent = '✅ JS LOADED (WEB TEST)';
        document.body.appendChild(indicator);

        // Debug logging function for web
        function debugLog(message, data = null) {
            const timestamp = new Date().toISOString();
            const logMessage = `[${timestamp}] ${message}`;
            const fullMessage = data ? `${logMessage} ${JSON.stringify(data)}` : logMessage;

            console.log('🚨 DEBUG:', fullMessage);

            // Show alerts for critical messages
            if (message.includes('CRITICAL') || message.includes('saveSettings')) {
                alert(`DEBUG: ${message}`);
            }
        }

        debugLog('🚨🚨🚨 CRITICAL: Web test JavaScript loaded with robust debugging');

        // Simplified settings UI class for testing
        class WebTestSettingsUI {
            constructor() {
                this.saveInProgress = false;
                this.initialize();
            }

            initialize() {
                debugLog('🚨 CRITICAL: WebTestSettingsUI.initialize() called');

                const saveBtn = document.getElementById('saveBtn');
                if (saveBtn) {
                    debugLog('🚨 CRITICAL: Save button found, adding event listener');
                    saveBtn.addEventListener('click', () => {
                        debugLog('🚨 CRITICAL: Save button clicked - calling saveSettings');
                        this.saveSettings();
                    });
                } else {
                    debugLog('🚨 CRITICAL ERROR: Save button not found in DOM');
                }
            }

            collectFormData() {
                const apiKeyInput = document.getElementById('apiKey');
                const autoSyncInput = document.getElementById('autoSync');
                const syncIntervalInput = document.getElementById('syncInterval');

                const formData = {
                    apiKey: apiKeyInput?.value.trim() || '',
                    autoSync: autoSyncInput?.checked || false,
                    syncInterval: parseInt(syncIntervalInput?.value) || 6
                };

                debugLog('🔍 DEBUG: Form data collected', {
                    hasApiKey: !!formData.apiKey,
                    apiKeyLength: formData.apiKey.length,
                    apiKeyEmpty: formData.apiKey === '',
                    autoSync: formData.autoSync,
                    syncInterval: formData.syncInterval
                });

                return formData;
            }

            async saveSettings() {
                debugLog('🚨🚨🚨 CRITICAL: saveSettings() method called - START OF EXECUTION');

                if (this.saveInProgress) {
                    debugLog('🚨 CRITICAL: Save already in progress, ignoring duplicate call');
                    return;
                }
                this.saveInProgress = true;

                const saveBtn = document.getElementById('saveBtn');
                if (saveBtn) {
                    saveBtn.disabled = true;
                    saveBtn.textContent = 'Saving...';
                }

                try {
                    // Collect form data
                    const formData = this.collectFormData();

                    // CRITICAL VALIDATION CHECK
                    if (!formData.apiKey || formData.apiKey.trim().length === 0) {
                        debugLog('🚨 CRITICAL: No API key provided, showing error and returning early', {
                            apiKey: formData.apiKey,
                            apiKeyType: typeof formData.apiKey,
                            apiKeyLength: formData.apiKey?.length || 0,
                            apiKeyTrimmed: formData.apiKey?.trim(),
                            apiKeyTrimmedLength: formData.apiKey?.trim().length || 0,
                            shouldReturn: true
                        });

                        alert('ERROR: API key is required.');
                        debugLog('🚨 CRITICAL: Returning early due to missing API key - EXECUTION SHOULD STOP HERE');
                        debugLog('🚨 CRITICAL: If you see any more logs after this, there is a bug in the flow');
                        return;
                    }

                    debugLog('🔍 DEBUG: API key validation passed, proceeding with validation flow');

                    // Simulate validation call
                    debugLog('🔍 DEBUG: Calling lifeboard.settings.save with validateOnly: true');
                    const validationResult = await window.lifeboard.settings.save(
                        'limitless',
                        formData,
                        null,
                        { validateOnly: true }
                    );

                    debugLog('🔍 DEBUG: Validation result received', validationResult);

                    if (!validationResult.success) {
                        debugLog('🚨 CRITICAL: Validation failed', validationResult);
                        alert(`ERROR: ${validationResult.error}`);
                        return;
                    }

                    // If validation passed, proceed with actual save
                    debugLog('🔍 DEBUG: Validation passed, proceeding with actual save');
                    const saveResult = await window.lifeboard.settings.save('limitless', formData);

                    if (saveResult) {
                        debugLog('✅ SUCCESS: Settings saved successfully');
                        alert('SUCCESS: Settings saved successfully!');
                    } else {
                        debugLog('❌ ERROR: Failed to save settings');
                        alert('ERROR: Failed to save settings');
                    }

                } catch (error) {
                    debugLog('💥 ERROR: Exception during save', error);
                    alert(`ERROR: ${error.message}`);
                } finally {
                    this.saveInProgress = false;
                    if (saveBtn) {
                        saveBtn.disabled = false;
                        saveBtn.textContent = 'Save Settings';
                    }
                    debugLog('🚨 CRITICAL: saveSettings() method completed - END OF EXECUTION');
                }
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('🚨 CRITICAL: DOM loaded, initializing WebTestSettingsUI');
            new WebTestSettingsUI();
        });

        // Also initialize immediately in case DOM is already loaded
        if (document.readyState === 'loading') {
            // DOM is still loading
        } else {
            // DOM is already loaded
            debugLog('🚨 CRITICAL: DOM already loaded, initializing WebTestSettingsUI immediately');
            new WebTestSettingsUI();
        }
    </script>
</body>
</html>
