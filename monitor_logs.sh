#!/bin/bash

# Monitor logs for the Limitless plugin bug debugging
# This script will tail the relevant log files and show critical messages

echo "🔍 Monitoring Lifeboard logs for Limitless plugin debugging..."
echo "📁 Log directory: logs/electron/"
echo "🚨 Looking for CRITICAL messages related to settings save..."
echo ""
echo "Press Ctrl+C to stop monitoring"
echo "=================================="

# Get today's date for log files
TODAY=$(date +%Y-%m-%d)

# Define log files to monitor
LOG_FILES=(
    "logs/electron/${TODAY}-desktop.log"
    "logs/electron/${TODAY}-plugin-ipc-handlers.log" 
    "logs/electron/${TODAY}-plugin-manager.log"
    "logs/electron/${TODAY}-SettingsManager.log"
    "logs/${TODAY}-limitless.log"
)

# Function to monitor a single log file
monitor_log() {
    local logfile="$1"
    if [[ -f "$logfile" ]]; then
        echo "📄 Monitoring: $logfile"
        tail -f "$logfile" | grep --line-buffered -E "(CRITICAL|🚨|settings.*save|limitless)" &
    else
        echo "⚠️  Log file not found: $logfile"
    fi
}

# Start monitoring all log files
for logfile in "${LOG_FILES[@]}"; do
    monitor_log "$logfile"
done

# Also monitor console output if the app is running
echo ""
echo "🖥️  Also monitoring console output..."
echo "📝 To test the bug:"
echo "   1. Open Limitless plugin settings"
echo "   2. Leave API key field empty"
echo "   3. Click 'Save Settings'"
echo "   4. Watch for CRITICAL messages below"
echo ""

# Wait for user to stop
wait
