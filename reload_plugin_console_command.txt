// Copy and paste this command into the Electron dev console (F12 → Console)
// This will reload the limitless plugin to pick up code changes

(async function reloadLimitlessPlugin() {
    console.log('🔄 Reloading limitless plugin...');
    
    try {
        // Check if we have access to the IPC
        if (typeof lifeboard !== 'undefined' && lifeboard.plugins && lifeboard.plugins.reload) {
            const result = await lifeboard.plugins.reload('limitless');
            if (result) {
                console.log('✅ Limitless plugin reloaded successfully via lifeboard API');
                console.log('🔍 Now try opening the settings modal to see if the new code is loaded');
                return true;
            }
        }
        
        // Fallback: try direct IPC if available
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            const result = await ipcRenderer.invoke('plugins:reload', 'limitless');
            
            if (result) {
                console.log('✅ Limitless plugin reloaded successfully via IPC');
                console.log('🔍 Now try opening the settings modal to see if the new code is loaded');
                return true;
            } else {
                console.log('❌ Failed to reload limitless plugin via IPC');
                return false;
            }
        }
        
        console.log('❌ No reload method available - you may need to restart the app');
        return false;
        
    } catch (error) {
        console.error('💥 Error reloading plugin:', error);
        console.log('❌ Plugin reload failed - you may need to restart the app');
        return false;
    }
})();
