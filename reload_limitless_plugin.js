/**
 * Reload Limitless Plugin Script
 * 
 * This script reloads the limitless plugin to pick up code changes
 * without restarting the entire Electron app.
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron');

async function reloadLimitlessPlugin() {
    console.log('🔄 Reloading limitless plugin...');
    
    try {
        const result = await ipcRenderer.invoke('plugins:reload', 'limitless');
        
        if (result) {
            console.log('✅ Limitless plugin reloaded successfully');
            console.log('🔍 Now try opening the settings modal to see if the new code is loaded');
        } else {
            console.log('❌ Failed to reload limitless plugin');
        }
        
        return result;
    } catch (error) {
        console.error('💥 Error reloading plugin:', error);
        return false;
    }
}

// Auto-run if this script is executed directly
if (typeof window !== 'undefined') {
    // Running in renderer process
    reloadLimitlessPlugin();
} else {
    // Export for use in other contexts
    module.exports = { reloadLimitlessPlugin };
}
