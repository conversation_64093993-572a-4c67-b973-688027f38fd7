#!/bin/bash

# Monitor the debug file for Limitless plugin debugging
echo "🔍 Monitoring debug_limitless.log for robust debugging output..."
echo "📁 Debug file: debug_limitless.log"
echo "🚨 This will show ALL debug messages from the plugin"
echo ""
echo "Press Ctrl+C to stop monitoring"
echo "=================================="

# Create debug file if it doesn't exist
touch debug_limitless.log

# Monitor the debug file
echo "📄 Monitoring: debug_limitless.log"
tail -f debug_limitless.log &

# Also monitor the main terminal output
echo ""
echo "🖥️  Also monitoring for console output..."
echo "📝 To test the bug:"
echo "   1. Restart the Electron app"
echo "   2. Open Limitless plugin settings"
echo "   3. Look for '✅ JS LOADED' indicator in top-right corner"
echo "   4. Leave API key field empty"
echo "   5. Click 'Save Settings'"
echo "   6. Watch for debug messages and alerts"
echo ""

# Wait for user to stop
wait
