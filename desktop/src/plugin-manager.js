const { app } = require('electron');
const { PluginLoader } = require('./PluginLoader');
const { factory: createLogger } = require('../core/logger/CoreLogger');
const { getLogger: getPluginAPILogger } = require('../core/pluginAPI');
const semver = require('semver');

// Note: Logger will be initialized in constructor with correct logDir
const CommandPalette = require('./CommandPalette.js');
const PluginEventBus = require('./EventBus.js');
const RibbonManager = require('./ui/RibbonManager.js');
const ModalManager = require('./ui/ModalManager.js');
const CommandPaletteUI = require('./ui/CommandPaletteUI.js');
const SettingsManager = require('./SettingsManager.js');
const StateManager = require('./storage/StateManager.js');
const PluginRegistry = require('./storage/PluginRegistry.js');
const PluginManagementUI = require('./ui/PluginManagementUI.js');
const SettingsModalUI = require('./ui/SettingsModalUI.js');
const MarketplaceManager = require('./marketplace/MarketplaceManager.js');
const PackageManager = require('./marketplace/PackageManager.js');

class PluginManager {
  constructor(lifeboardApp, options = {}) {
    this.app = lifeboardApp;
    this.pluginDirs = [];
    this.isInitialized = false;

    // If logDir is provided, create a new logger with the correct directory
    if (options.logDir) {
      const { CoreLogger } = require('../core/logger/CoreLogger');
      this.log = new CoreLogger({
        component: 'plugin-manager',
        logDir: options.logDir,
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('plugin-manager');
    }

    this.commandPalette = new CommandPalette();
    this.eventBus = new PluginEventBus();
    this.ribbonManager = new RibbonManager(options.logDir);
    this.modalManager = new ModalManager(options.logDir);
    this.commandPaletteUI = new CommandPaletteUI(this.commandPalette, options.logDir);

    // M5 Components - Settings Storage & Plugin Management
    this.settingsManager = new SettingsManager(options.logDir);
    this.stateManager = null; // Will be initialized in setupPluginDirectories
    this.pluginRegistry = null; // Will be initialized in setupPluginDirectories
    this.pluginManagementUI = new PluginManagementUI(options.logDir);
    this.settingsModalUI = new SettingsModalUI(options.logDir);

    // M6 Components - Marketplace & Package Management
    this.marketplaceManager = null; // Will be initialized in setupPluginDirectories
    this.packageManager = null; // Will be initialized in setupPluginDirectories

    // Store logDir for later use
    this.logDir = options.logDir;

    // Initialize PluginLoader after all dependencies are set up
    this.pluginLoader = new PluginLoader(
      this.log,
      this.commandPalette,
      this.eventBus,
      this.ribbonManager,
      this.modalManager,
      this.commandPaletteUI,
      this.settingsManager,
      this
    );
  }

  loadPlugins() {
    this.pluginLoader.loadPlugins(this.pluginDirs);
  }

  loadPluginFromDirectory(pluginDir) {
    const manifestPath = path.join(pluginDir, 'manifest.json');

    if (!fs.existsSync(manifestPath)) {
      this.log.DEBUG(`No manifest.json found in ${pluginDir}`, {
        component: 'PluginManager',
        pluginDir: pluginDir
      });
      return;
    }

    try {
      const manifestContent = fs.readFileSync(manifestPath, 'utf8');
      const manifest = JSON.parse(manifestContent);

      // Validate manifest structure
      if (!this.validateManifest(manifest)) {
        this.log.WARN(`Invalid manifest in ${pluginDir}`, {
          component: 'PluginManager',
          pluginDir: pluginDir,
          error: error.message
        });
        return;
      }

      // Check app version compatibility
      const appVersion = app.getVersion();
      if (manifest.minAppVersion && !semver.gte(appVersion, manifest.minAppVersion)) {
        this.log.WARN(`Plugin ${manifest.name} requires app version ${manifest.minAppVersion}, current: ${appVersion}`, {
          component: 'PluginManager',
          pluginName: manifest.name,
          requiredVersion: manifest.minAppVersion,
          currentVersion: appVersion
        });
        return;
      }

      // Check if plugin is already loaded
      if (this.plugins.has(manifest.id)) {
        this.log.WARN(`Plugin ${manifest.id} is already loaded`, {
          component: 'PluginManager',
          pluginId: manifest.id
        });
        return;
      }

      // Load main plugin file
      const mainPath = path.join(pluginDir, manifest.main);
      if (!fs.existsSync(mainPath)) {
        this.log.ERROR(`Main file not found for plugin ${manifest.id}: ${mainPath}`, {
          component: 'PluginManager',
          pluginId: manifest.id,
          mainPath: mainPath
        });
        return;
      }

      try {
        const code = fs.readFileSync(mainPath, 'utf8');
        const pluginContext = this.createPluginContext(manifest, pluginDir);
        const pluginScript = new vm.Script(code, { filename: mainPath });

        // Execute plugin in sandbox
        pluginScript.runInContext(pluginContext);

        // Store plugin info
        this.plugins.set(manifest.id, {
          manifest,
          context: pluginContext,
          directory: pluginDir,
          enabled: false, // Start plugins disabled by default
          loadedAt: new Date()
        });

        this.log.INFO(`Successfully loaded plugin: ${manifest.name} v${manifest.version}`, {
          component: 'PluginManager',
          pluginId: manifest.id,
          pluginName: manifest.name,
          version: manifest.version
        });

      } catch (error) {
        this.log.ERROR(`Error executing plugin ${manifest.id}`, {
          component: 'PluginManager',
          pluginId: manifest.id,
          error: error.message,
          stack: error.stack
        });
      }

    } catch (error) {
      this.log.ERROR(`Error loading plugin from ${pluginDir}`, {
        component: 'PluginManager',
        pluginDir: pluginDir,
        error: error.message,
        stack: error.stack
      });
    }
  }

  createPluginContext(manifest, pluginDir) {
    const pluginAPI = this.createPluginAPI(manifest, pluginDir);

    const sandbox = {
      console: {
        log: (...args) => this.log.INFO(`[Plugin:${manifest.id}]`, { message: args.join(' ') }),
        error: (...args) => this.log.ERROR(`[Plugin:${manifest.id}]`, { message: args.join(' ') }),
        warn: (...args) => this.log.WARN(`[Plugin:${manifest.id}]`, { message: args.join(' ') }),
        info: (...args) => this.log.INFO(`[Plugin:${manifest.id}]`, { message: args.join(' ') })
      },
      require: this.createSafeRequire(pluginDir, manifest.permissions || []),
      process: {
        env: process.env,
        platform: process.platform,
        version: process.version
      },
      Buffer,
      setTimeout,
      clearTimeout,
      setInterval,
      clearInterval,
      // Expose the Plugin API
      PluginAPI: pluginAPI,
      // Common globals
      global: {},
      exports: {},
      module: { exports: {} }
    };

    return vm.createContext(sandbox);
  }

  createPluginAPI(manifest, pluginDir) {
    this.log.DEBUG('Creating Plugin API', { pluginId: manifest.id, pluginDir });

    return {
      app: {
        getName: () => 'Lifeboard',
        getVersion: () => app.getVersion(),
        getPlatform: () => process.platform
      },
      workspace: {
        // Will be implemented in M3
        version: '0.1.0-placeholder'
      },
      // Expose CoreLogger through PluginAPI
      logger: getPluginAPILogger(manifest.id),
      commands: {
        register: (commandId, handler) => {
          const fullCommandId = `${manifest.id}:${commandId}`;
          this.commandPalette.registerCommand(fullCommandId, handler);
          this.log.INFO(`[Plugin:${manifest.id}] Registered command: ${commandId}`, {
            component: 'PluginManager',
            pluginId: manifest.id,
            commandId: commandId
          });
        },
        execute: (commandId) => {
          this.commandPalette.executeCommand(`${manifest.id}:${commandId}`);
        },
        setMetadata: (commandId, metadata) => {
          const fullCommandId = `${manifest.id}:${commandId}`;
          this.commandPaletteUI.setCommandMetadata(fullCommandId, metadata);
          this.log.DEBUG(`[Plugin:${manifest.id}] Set metadata for command: ${commandId}`, {
            component: 'PluginManager',
            pluginId: manifest.id,
            commandId: commandId
          });
        }
      },
      events: {
        on: (event, handler) => {
          this.eventBus.addPluginListener(manifest.id, event, handler);
        },
        emit: (event, data) => {
          this.eventBus.emitPluginEvent(manifest.id, event, data);
        },
        off: (event, handler) => {
          this.eventBus.removeListener(event, handler);
        }
      },
      ui: {
        addRibbonIcon: (icon, title, callback) => {
          return this.ribbonManager.addRibbonIcon(manifest.id, icon, title, callback);
        },
        removeRibbonIcon: (iconId) => {
          return this.ribbonManager.removeRibbonIcon(iconId);
        },
        showModal: (config) => {
          return this.modalManager.showModal(manifest.id, config);
        },
        closeModal: (modalId) => {
          return this.modalManager.closeModal(modalId);
        }
      },
      storage: {
        // M5 Enhanced Settings Storage
        loadData: () => this.loadPluginData(manifest.id),
        saveData: (data) => this.savePluginData(manifest.id, data),
        loadSettings: (schema) => this.settingsManager.load(manifest.id, schema),
        saveSettings: (data, schema) => this.settingsManager.save(manifest.id, data, schema),
        resetSettings: () => this.resetPluginSettings(manifest.id)
      },
      network: {
        // Basic fetch functionality if network permission granted
        fetch: manifest.permissions && manifest.permissions.includes('network')
          ? this.createNetworkFetch()
          : undefined
      },
      // Plugin metadata
      manifest: {
        id: manifest.id,
        name: manifest.name,
        version: manifest.version,
        permissions: manifest.permissions || []
      }
    };
  }

  initialize() {
    this.log.INFO('PluginManager: Initializing', {
      component: 'PluginManager'
    });

    // Setup plugin directories
    this.setupPluginDirectories();

    // Load plugins from manifest files
    this.loadPlugins();

    this.isInitialized = true;

    this.log.INFO('PluginManager: Initialized with plugin loading and M4 UI systems', {
      component: 'PluginManager'
    });
  }

  // Set notification function for UI managers
  setNotifyRenderer(notifyFn) {
    this.ribbonManager.setNotifyRenderer(notifyFn);
    this.modalManager.setNotifyRenderer(notifyFn);
    this.commandPaletteUI.setNotifyRenderer(notifyFn);
    // M5 UI components
    this.pluginManagementUI.setNotifyRenderer(notifyFn);
    this.settingsModalUI.setNotifyRenderer(notifyFn);
    this.log.DEBUG('PluginManager: Set renderer notification for UI managers (including M5 components)', {
      component: 'PluginManager'
    });
  }

  setupPluginDirectories() {
    const userDataPath = app.getPath('userData');
    const devPluginPath = path.join(process.cwd(), 'plugins');

    // Production plugins directory
    const prodPluginPath = path.join(userDataPath, 'plugins');

    this.pluginDirs = [
      prodPluginPath,  // Installed plugins
      devPluginPath    // Development plugins (symlinks)
    ];

    // Create directories if they don't exist
    this.pluginDirs.forEach(dir => {
      try {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          this.log.INFO(`Created plugin directory: ${dir}`, { directory: dir });
        }
      } catch (error) {
        this.log.ERROR(`Failed to create plugin directory ${dir}:`, {
          directory: dir,
          error: error.message,
          stack: error.stack
        });
      }
    });

    // Initialize M5 storage components with the base plugin directory
    const basePluginDir = prodPluginPath;
    this.stateManager = new StateManager(basePluginDir, this.logDir);
    this.pluginRegistry = new PluginRegistry(basePluginDir, this.logDir);

    // Set up event listeners for state changes
    this.stateManager.on('stateChanged', (event) => {
      this.handlePluginStateChange(event);
    });

    this.stateManager.on('error', (event) => {
      this.log.ERROR(`[PluginManager] State manager error for plugin ${event.pluginId}:`, {
        pluginId: event.pluginId,
        error: event.error?.message || event.error,
        stack: event.error?.stack
      });
    });

    // Initialize M6 marketplace components
    this.marketplaceManager = new MarketplaceManager({
      pluginDir: basePluginDir,
      logger: {
        info: (...args) => this.log.INFO('[MarketplaceManager]', { message: args.join(' ') }),
        warn: (...args) => this.log.WARN('[MarketplaceManager]', { message: args.join(' ') }),
        error: (...args) => this.log.ERROR('[MarketplaceManager]', { message: args.join(' ') }),
        debug: (...args) => this.log.DEBUG('[MarketplaceManager]', { message: args.join(' ') })
      }
    });

    this.packageManager = new PackageManager({
      logger: {
        info: (...args) => this.log.INFO('[PackageManager]', { message: args.join(' ') }),
        warn: (...args) => this.log.WARN('[PackageManager]', { message: args.join(' ') }),
        error: (...args) => this.log.ERROR('[PackageManager]', { message: args.join(' ') }),
        debug: (...args) => this.log.DEBUG('[PackageManager]', { message: args.join(' ') })
      }
    });

    this.log.INFO('Plugin directories configured:', { pluginDirs: this.pluginDirs });
    this.log.INFO('[PluginManager] M5 storage components initialized', { basePluginDir });
    this.log.INFO('[PluginManager] M6 marketplace components initialized', { basePluginDir });
  }

  // List all loaded plugins with their manifest data
  listPlugins() {
    if (!this.isInitialized) {
      return [];
    }

    const pluginList = [];
    this.pluginLoader.getPlugins().forEach((plugin, id) => {
      pluginList.push({
        id: plugin.manifest.id,
        name: plugin.manifest.name,
        version: plugin.manifest.version,
        enabled: plugin.enabled,
        permissions: plugin.manifest.permissions || [],
        loadedAt: plugin.loadedAt,
        directory: plugin.directory
      });
    });

    return pluginList;
  }

  // M5: Update plugin management UI with the latest plugin states
  updatePluginManagementUI() {
   this.log.DEBUG('[PluginManager] Updating plugin management UI', { pluginCount: this.plugins.size });

    try {
      const allPluginStates = Array.from(this.pluginLoader.getPlugins().keys()).map(pluginId => {
        const pluginState = this.stateManager.getPluginState(pluginId);
        const pluginEntry = this.pluginLoader.getPlugin(pluginId);

        return {
          id: pluginId,
          name: pluginEntry?.manifest?.name || pluginId,
          version: pluginEntry?.manifest?.version || '0.0.0',
          state: pluginState.state,
          enabled: pluginState.state === 'enabled',
          permissions: pluginState.permissions?.granted || [],
          lastActivity: pluginState.statistics?.lastActivity || null,
          loadedAt: pluginEntry?.loadedAt || null,
          error: null, // TODO: Detect and assign errors if present
          installDate: pluginState.createdAt,
          tags: pluginEntry?.manifest?.tags || []
        };
      });

      // Update plugin management interface
      this.pluginManagementUI.updatePlugins(allPluginStates);
    } catch (error) {
     this.log.ERROR('[PluginManager] Failed to update plugin management UI:', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  // Enable a plugin (M5 Enhanced with State Management)
  enablePlugin(pluginId) {
    const corrId = `plugin-enable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

   this.log.INFO('Plugin enable request', {
      pluginId,
      action: 'enable',
      corrId
    });

    if (!this.pluginLoader.hasPlugin(pluginId)) {
     this.log.WARN('Plugin enable failed: plugin not found', {
        pluginId,
        corrId
      });
      return false;
    }

    const plugin = this.pluginLoader.getPlugin(pluginId);
    if (plugin.enabled) {
     this.log.DEBUG('Plugin already enabled', {
        pluginId,
        corrId
      });
      return true;
    }

    try {
      // Update state manager
      if (this.stateManager) {
        const success = this.stateManager.enablePlugin(pluginId);
        if (!success) {
         this.log.ERROR('State manager enable failed', {
            pluginId,
            corrId
          });
          return false;
        }
      }

      // Update registry
      if (this.pluginRegistry) {
        this.pluginRegistry.updatePluginState(pluginId, 'enabled');
      }

      plugin.enabled = true;

      // Update plugin management UI
      this.updatePluginManagementUI();

     this.log.INFO('Plugin enabled successfully', {
        pluginId,
        corrId
      });

      // Log lifecycle event
     this.log.INFO('Plugin lifecycle: Plugin enabled', {
        pluginId,
        action: 'enable',
        timestamp: new Date().toISOString(),
        corrId
      });

      return true;
    } catch (error) {
     this.log.ERROR('Plugin enable failed', {
        pluginId,
        error: error.message,
        stack: error.stack,
        corrId
      });
      return false;
    }
  }

  // Disable a plugin (M5 Enhanced with State Management)
  disablePlugin(pluginId) {
    const corrId = `plugin-disable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

   this.log.INFO('Plugin disable request', {
      pluginId,
      action: 'disable',
      corrId
    });

    if (!this.pluginLoader.hasPlugin(pluginId)) {
     this.log.WARN('Plugin disable failed: plugin not found', {
        pluginId,
        corrId
      });
      return false;
    }

    const plugin = this.pluginLoader.getPlugin(pluginId);
    if (!plugin.enabled) {
     this.log.DEBUG('Plugin already disabled', {
        pluginId,
        corrId
      });
      return true;
    }

    try {
      // Clean up plugin UI elements
      this.cleanupPluginUI(pluginId);

      // Update state manager
      if (this.stateManager) {
        const success = this.stateManager.disablePlugin(pluginId);
        if (!success) {
         this.log.ERROR('State manager disable failed', {
            pluginId,
            corrId
          });
          return false;
        }
      }

      // Update registry
      if (this.pluginRegistry) {
        this.pluginRegistry.updatePluginState(pluginId, 'disabled');
      }

      plugin.enabled = false;

      // Update plugin management UI
      this.updatePluginManagementUI();

     this.log.INFO('Plugin disabled successfully', {
        pluginId,
        corrId
      });

      // Log lifecycle event
     this.log.INFO('Plugin lifecycle: Plugin disabled', {
        pluginId,
        action: 'disable',
        timestamp: new Date().toISOString(),
        corrId
      });

      return true;
    } catch (error) {
     this.log.ERROR('Plugin disable failed', {
        pluginId,
        error: error.message,
        stack: error.stack,
        corrId
      });
      return false;
    }
  }

  // Clean up UI elements for a plugin
  cleanupPluginUI(pluginId) {
    // Remove ribbon icons
    const removedIcons = this.ribbonManager.removePluginIcons(pluginId);

    // Close plugin modals
    const closedModals = this.modalManager.closePluginModals(pluginId);

    // Remove command metadata
    const commands = this.commandPalette.listCommands();
    commands.forEach(command => {
      if (command.id.startsWith(`${pluginId}:`)) {
        this.commandPaletteUI.removeCommandMetadata(command.id);
      }
    });

   this.log.DEBUG(`[PluginManager] Cleaned up UI for plugin ${pluginId}: ${removedIcons} icons, ${closedModals} modals`, {
      pluginId,
      removedIcons,
      closedModals
    });
  }

  // Utility method for future use
  getPluginPath(pluginId) {
    for (const dir of this.pluginDirs) {
      const pluginPath = path.join(dir, pluginId);
      if (fs.existsSync(pluginPath)) {
        return pluginPath;
      }
    }
    return null;
  }

  // Method to validate plugin manifest
  validateManifest(manifest) {
    // Required fields
    const required = ['id', 'name', 'version', 'main'];
    if (!required.every(field => manifest[field])) {
      return false;
    }

    // Validate version format
    if (!semver.valid(manifest.version)) {
     this.log.WARN(`Invalid version format in manifest: ${manifest.version}`, {
        manifest: manifest.version,
        pluginId: manifest.id
      });
      return false;
    }

    // Validate minAppVersion if present
    if (manifest.minAppVersion && !semver.valid(manifest.minAppVersion)) {
     this.log.WARN(`Invalid minAppVersion format in manifest: ${manifest.minAppVersion}`, {
        minAppVersion: manifest.minAppVersion,
        pluginId: manifest.id
      });
      return false;
    }

    // Validate permissions if present
    if (manifest.permissions) {
      const validPermissions = ['workspace', 'network', 'filesystem', 'system'];
      const invalidPerms = manifest.permissions.filter(p => !validPermissions.includes(p));
      if (invalidPerms.length > 0) {
       this.log.WARN(`Invalid permissions in manifest: ${invalidPerms.join(', ')}`, {
          invalidPermissions: invalidPerms,
          pluginId: manifest.id
        });
        return false;
      }
    }

    return true;
  }

  // Create a safe require function for plugins
  createSafeRequire(pluginDir, permissions) {
    const originalRequire = require;

    return function(moduleName) {
      // Allow basic Node.js modules
      const allowedModules = [
        'path', 'fs', 'util', 'crypto', 'os',
        'events', 'stream', 'querystring', 'url'
      ];

      // Allow network modules if permission granted
      if (permissions.includes('network')) {
        allowedModules.push('http', 'https', 'net', 'dns');
      }

      // Allow filesystem modules if permission granted
      if (permissions.includes('filesystem')) {
        allowedModules.push('fs', 'path');
      }

      // Block dangerous modules
      const blockedModules = [
        'child_process', 'cluster', 'process', 'vm',
        'electron', 'node-gyp'
      ];

      if (blockedModules.includes(moduleName)) {
        throw new Error(`Module '${moduleName}' is not allowed in plugins`);
      }

      // Allow relative requires from plugin directory
      if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
        const resolvedPath = path.resolve(pluginDir, moduleName);
        if (!resolvedPath.startsWith(pluginDir)) {
          throw new Error('Plugin cannot require files outside its directory');
        }
        return originalRequire(resolvedPath);
      }

      // Check if module is allowed
      if (allowedModules.includes(moduleName)) {
        return originalRequire(moduleName);
      }

      throw new Error(`Module '${moduleName}' is not allowed in plugins`);
    };
  }

  // Load plugin-specific data
  loadPluginData(pluginId) {
    const userDataPath = app.getPath('userData');
    const pluginDataPath = path.join(userDataPath, 'plugins', pluginId, 'data.json');

    try {
      if (fs.existsSync(pluginDataPath)) {
        const data = fs.readFileSync(pluginDataPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
     this.log.ERROR(`Error loading data for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
    }

    return {};
  }

  // Save plugin-specific data
  savePluginData(pluginId, data) {
    const userDataPath = app.getPath('userData');
    const pluginDataDir = path.join(userDataPath, 'plugins', pluginId);
    const pluginDataPath = path.join(pluginDataDir, 'data.json');

    try {
      // Ensure directory exists
      if (!fs.existsSync(pluginDataDir)) {
        fs.mkdirSync(pluginDataDir, { recursive: true });
      }

      fs.writeFileSync(pluginDataPath, JSON.stringify(data, null, 2));
     this.log.DEBUG(`Saved data for plugin ${pluginId}`, { pluginId });
      return true;
    } catch (error) {
     this.log.ERROR(`Error saving data for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  // Reload a specific plugin
  reloadPlugin(pluginId) {
    return this.pluginLoader.reloadPlugin(pluginId);
  }

  // Create network fetch function for plugins with network permission
  createNetworkFetch() {
    try {
      // Try to use node-fetch if available
      const fetch = require('node-fetch');
      return fetch;
    } catch (error) {
      // Fallback to a simple placeholder function
     this.log.WARN('node-fetch not available, providing placeholder network API', {
        component: 'PluginManager'
      });
      return function() {
        throw new Error('Network functionality requires node-fetch to be installed');
      };
    }
  }

  // Get plugin directories (for testing/debugging)
  getPluginDirectories() {
    return this.pluginDirs;
  }

  // M5: Handle plugin state change events
  handlePluginStateChange(event) {
   this.log.INFO(`[PluginManager] Plugin state changed: ${event.pluginId} ${event.oldState} -> ${event.newState}`, {
      pluginId: event.pluginId,
      oldState: event.oldState,
      newState: event.newState
    });

    try {
      // Update plugin registry
      if (this.pluginRegistry) {
        this.pluginRegistry.updatePluginState(event.pluginId, event.newState);
      }

      // Update plugin management UI
      this.updatePluginManagementUI();

      // Update statistics
      if (this.stateManager) {
        this.stateManager.updateStatistics(event.pluginId, {
          lastActivity: event.timestamp
        });
      }
    } catch (error) {
     this.log.ERROR(`[PluginManager] Error handling state change for ${event.pluginId}:`, {
        pluginId: event.pluginId,
        error: error.message,
        stack: error.stack
      });
    }
  }

  // M5: Reset plugin settings to defaults
  resetPluginSettings(pluginId) {
   this.log.INFO(`[PluginManager] Resetting settings for plugin: ${pluginId}`, { pluginId });

    try {
      // Delete existing settings
      if (this.settingsManager.hasSettings(pluginId)) {
        this.settingsManager.deleteSettings(pluginId);
      }

     this.log.INFO(`[PluginManager] Reset settings for plugin: ${pluginId}`, { pluginId });
      return true;
    } catch (error) {
     this.log.ERROR(`[PluginManager] Failed to reset settings for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  // M5: Get comprehensive plugin information including state and settings
  getPluginInfo(pluginId) {
   this.log.DEBUG(`[PluginManager] Getting comprehensive info for plugin: ${pluginId}`, { pluginId });

    try {
      const plugin = this.plugins.get(pluginId);
      if (!plugin) {
        return null;
      }

      const state = this.stateManager ? this.stateManager.getPluginState(pluginId) : null;
      const registryEntry = this.pluginRegistry ? this.pluginRegistry.getPlugin(pluginId) : null;

      return {
        id: pluginId,
        manifest: plugin.manifest,
        enabled: plugin.enabled,
        directory: plugin.directory,
        loadedAt: plugin.loadedAt,
        state: state,
        registryEntry: registryEntry
      };
    } catch (error) {
     this.log.ERROR(`[PluginManager] Failed to get plugin info for ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return null;
    }
  }

  // M5: Show plugin settings modal
  showPluginSettings(pluginId) {
   this.log.INFO(`[PluginManager] Showing settings for plugin: ${pluginId}`, { pluginId });

    try {
      const pluginInfo = this.getPluginInfo(pluginId);
      if (!pluginInfo) {
       this.log.ERROR(`[PluginManager] Plugin not found: ${pluginId}`, { pluginId });
        return null;
      }

      // Load current settings
      const currentSettings = this.settingsManager.load(pluginId);

      // Show settings modal
      const modalId = this.settingsModalUI.showSettingsModal(
        pluginId,
        currentSettings,
        pluginInfo.manifest
      );

     this.log.INFO(`[PluginManager] Settings modal ${modalId} shown for plugin: ${pluginId}`, {
        modalId,
        pluginId
      });
      return modalId;
    } catch (error) {
     this.log.ERROR(`[PluginManager] Failed to show settings for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return null;
    }
  }

  // M5: Save plugin settings from modal
  async savePluginSettings(pluginId, settings, validateOnly = false) {
    const corrId = `plugin-settings-save-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

   this.log.INFO('Plugin settings save initiated', {
      pluginId,
      settingsKeys: Object.keys(settings).filter(key => key !== 'apiKey'),
      hasApiKey: !!settings.apiKey,
      validateOnly,
      corrId
    });

    try {
      if (validateOnly) {
        // Validation-only mode: check if plugin has validateSettings method
        const pluginInstance = this.plugins.get(pluginId);

        if (!pluginInstance) {
          this.log.WARN('Plugin validation failed: Plugin not loaded', {
            pluginId,
            corrId
          });
          return { success: false, error: 'Plugin not loaded' };
        }

        if (typeof pluginInstance.validateSettings !== 'function') {
          this.log.WARN('Plugin validation failed: validateSettings method not found', {
            pluginId,
            corrId
          });
          return { success: false, error: 'Plugin does not support validation' };
        }

        // Call plugin's validation method
        const validationResult = await pluginInstance.validateSettings(settings);

        this.log.INFO('Plugin settings validation completed', {
          pluginId,
          success: validationResult.success,
          corrId
        });

        return validationResult;
      } else {
        // Normal save mode: persist settings
        const success = this.settingsManager.save(pluginId, settings);

        if (success) {
          // Update state manager with activity
          if (this.stateManager) {
            this.stateManager.updateStatistics(pluginId, {
              lastActivity: new Date().toISOString(),
              lastSettingsUpdate: new Date().toISOString()
            });
          }

         this.log.INFO('Plugin settings saved successfully', {
            pluginId,
            corrId
          });

          // Log configuration change audit trail
         this.log.INFO('Configuration change: Plugin settings updated', {
            pluginId,
            action: 'settings_save',
            timestamp: new Date().toISOString(),
            corrId
          });

        } else {
         this.log.WARN('Plugin settings save returned false', {
            pluginId,
            corrId
          });
        }

        return success;
      }
    } catch (error) {
     this.log.ERROR('Plugin settings save failed', {
        pluginId,
        error: error.message,
        stack: error.stack,
        validateOnly,
        corrId
      });
      return validateOnly ?
        { success: false, error: error.message } :
        false;
    }
  }

  // M4 UI Manager Access Methods

  // Ribbon Manager access
  getRibbonManager() {
    return this.ribbonManager;
  }

  // Modal Manager access
  getModalManager() {
    return this.modalManager;
  }

  // Command Palette UI access
  getCommandPaletteUI() {
    return this.commandPaletteUI;
  }

  // M5: Plugin Management UI access
  getPluginManagementUI() {
    return this.pluginManagementUI;
  }

  // M5: Settings Modal UI access
  getSettingsModalUI() {
    return this.settingsModalUI;
  }

  // M5: Settings Manager access
  getSettingsManager() {
    return this.settingsManager;
  }

  // M5: State Manager access
  getStateManager() {
    return this.stateManager;
  }

  // M5: Plugin Registry access
  getPluginRegistry() {
    return this.pluginRegistry;
  }

  // M6 Marketplace Integration Methods

  /**
   * Search for plugins in the marketplace
   * @param {string} query Search query
   * @param {Object} filters Search filters
   * @returns {Array} Array of matching plugins
   */
  async searchMarketplace(query, filters = {}) {
    try {
     this.log.INFO('[PluginManager] Searching marketplace', { query, filters });
      await this.marketplaceManager.initialize();
      return await this.marketplaceManager.searchPlugins(query, filters);
    } catch (error) {
     this.log.ERROR('[PluginManager] Marketplace search failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Install a plugin from the marketplace
   * @param {string} pluginId Plugin identifier
   * @param {Object} options Installation options
   * @returns {Object} Installation result
   */
  async installFromMarketplace(pluginId, options = {}) {
    try {
     this.log.INFO('[PluginManager] Installing plugin from marketplace', { pluginId, options });

      await this.marketplaceManager.initialize();
      const result = await this.marketplaceManager.installPlugin(pluginId, options);

      if (result.success) {
        // Reload plugins to include the newly installed plugin
        this.loadPlugins();
        this.updatePluginManagementUI();
      }

      return result;
    } catch (error) {
     this.log.ERROR('[PluginManager] Marketplace installation failed', {
        pluginId,
        error: error.message
      });
      throw error;
    }
  }



  /**
   * Check for plugin updates
   * @param {string|null} pluginId Specific plugin to check, or null for all
   * @returns {Array} Available updates
   */
  async checkForUpdates(pluginId = null) {
    try {
     this.log.INFO('[PluginManager] Checking for plugin updates', { pluginId });
      await this.marketplaceManager.initialize();
      return await this.marketplaceManager.checkForUpdates(pluginId);
    } catch (error) {
     this.log.ERROR('[PluginManager] Update check failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Get plugin details from marketplace
   * @param {string} pluginId Plugin identifier
   * @returns {Object} Plugin details
   */
  async getMarketplacePluginDetails(pluginId) {
    try {
      await this.marketplaceManager.initialize();
      return await this.marketplaceManager.getPluginDetails(pluginId);
    } catch (error) {
     this.log.ERROR('[PluginManager] Failed to get marketplace plugin details', {
        pluginId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get marketplace statistics
   * @returns {Object} Marketplace statistics
   */
  async getMarketplaceStats() {
    try {
      await this.marketplaceManager.initialize();
      return await this.marketplaceManager.getMarketplaceStats();
    } catch (error) {
     this.log.ERROR('[PluginManager] Failed to get marketplace stats', { error: error.message });
      throw error;
    }
  }

  /**
   * Create a plugin package
   * @param {string} sourcePath Path to plugin source
   * @param {Object} options Packaging options
   * @returns {Object} Package creation result
   */
  async createPackage(sourcePath, options = {}) {
    try {
     this.log.INFO('[PluginManager] Creating plugin package', { sourcePath, options });
      await this.packageManager.initialize();
      return await this.packageManager.createPackage(sourcePath, options);
    } catch (error) {
     this.log.ERROR('[PluginManager] Package creation failed', {
        sourcePath,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Verify a plugin package
   * @param {string} packagePath Path to package file
   * @param {Object} expectedMetadata Expected package metadata
   * @returns {Object} Verification result
   */
  async verifyPackage(packagePath, expectedMetadata = {}) {
    try {
      await this.packageManager.initialize();
      return await this.packageManager.verifyPackage(packagePath, expectedMetadata);
    } catch (error) {
     this.log.ERROR('[PluginManager] Package verification failed', {
        packagePath,
        error: error.message
      });
      throw error;
    }
  }

  // M6: Marketplace Manager access
  getMarketplaceManager() {
    return this.marketplaceManager;
  }

  // M6: Package Manager access
  getPackageManager() {
    return this.packageManager;
  }

  getUIStats() {
    const stats = {
      ribbons: this.ribbonManager.getStats(),
      modals: this.modalManager.getStats(),
      commandPalette: this.commandPaletteUI.getStats(),
      // M5 Statistics
      pluginManagement: this.pluginManagementUI.getStatistics(),
      settingsModals: this.settingsModalUI.getStatistics()
    };

    // Add M5 storage statistics if available
    if (this.stateManager) {
      stats.stateManager = {
        cacheSize: this.stateManager.getCacheSize()
      };
    }

    if (this.settingsManager) {
      stats.settingsManager = {
        cacheSize: this.settingsManager.getCacheSize()
      };
    }

    if (this.pluginRegistry) {
      stats.pluginRegistry = this.pluginRegistry.getStatistics();
    }

    return stats;
  }
}

module.exports = { PluginManager };
