/**
 * Test Case: Limitless Settings Validation Failure
 * 
 * This test reproduces the bug where the Limitless plugin incorrectly
 * displays a success message when attempting to save settings with an empty API key.
 * 
 * Expected: Should show error "API key is required"
 * Actual: Shows success message "Settings saved for limitless"
 */

const { JSDOM } = require('jsdom');
const path = require('path');
const fs = require('fs');

describe('Limitless Settings Validation', () => {
  let dom;
  let window;
  let document;
  let LimitlessSettingsUI;
  let mockPluginAPI;
  let mockLifeboard;
  let settingsInstance;

  beforeEach(() => {
    // Create DOM environment
    dom = new JSDOM(`
      <!DOCTYPE html>
      <html>
        <head><title>Test</title></head>
        <body>
          <input id="apiKey" type="password" value="" />
          <input id="syncInterval" type="range" value="6" />
          <input id="autoSync" type="checkbox" checked />
          <input id="maxRecords" type="number" value="1000" />
          <select id="timezone">
            <option value="UTC" selected>UTC</option>
          </select>
          <input id="enableDebugLogging" type="checkbox" />
          <input id="aiEnhancement" type="checkbox" checked />
          <button id="saveBtn">Save Settings</button>
          <div id="validationStatus"></div>
        </body>
      </html>
    `);

    window = dom.window;
    document = window.document;
    global.window = window;
    global.document = document;

    // Mock console methods to capture logs
    global.console = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn()
    };

    // Mock plugin API
    mockPluginAPI = {
      storage: {
        loadData: jest.fn(() => ({})),
        saveData: jest.fn(() => true)
      },
      getLogger: jest.fn(() => null),
      commands: {
        execute: jest.fn()
      }
    };

    // Mock lifeboard global (this is key to the bug)
    mockLifeboard = {
      settings: {
        save: jest.fn()
      }
    };

    // Set up globals
    global.lifeboard = mockLifeboard;
    window.parent = window;
    window.parent.lifeboard = mockLifeboard;
    window.parent.pluginAPI = mockPluginAPI;

    // Mock alert function
    global.alert = jest.fn();

    // Load and initialize the settings UI
    const settingsUIPath = path.join(__dirname, '../desktop/plugins/limitless/src/settings-ui.js');
    const settingsUICode = fs.readFileSync(settingsUIPath, 'utf8');
    
    // Remove the automatic initialization at the end
    const modifiedCode = settingsUICode.replace('new LimitlessSettingsUI();', '');
    
    // Evaluate the code in our mock environment
    eval(modifiedCode);
    
    // Manually create instance
    settingsInstance = new LimitlessSettingsUI();
  });

  afterEach(() => {
    dom.window.close();
    delete global.window;
    delete global.document;
    delete global.lifeboard;
    delete global.console;
    delete global.alert;
  });

  describe('Bug Reproduction: Empty API Key Validation', () => {
    
    test('should show error when API key is empty', async () => {
      // Arrange: Set up empty API key
      const apiKeyInput = document.getElementById('apiKey');
      apiKeyInput.value = '';
      
      // Mock lifeboard validation to return failure for empty key
      mockLifeboard.settings.save.mockResolvedValue({
        success: false,
        error: 'API key is required'
      });

      // Act: Attempt to save settings
      await settingsInstance.saveSettings();

      // Assert: Should show error, not success
      expect(global.console.log).toHaveBeenCalledWith(
        expect.stringContaining('🚨 DEBUG: No API key provided, showing error and returning early')
      );
      
      // Should not call lifeboard validation with empty key
      expect(mockLifeboard.settings.save).not.toHaveBeenCalled();
      
      // Should show error modal
      expect(global.alert).toHaveBeenCalledWith(
        expect.stringContaining('API key is required')
      );
    });

    test('should call validation service when API key is provided', async () => {
      // Arrange: Set up valid API key
      const apiKeyInput = document.getElementById('apiKey');
      apiKeyInput.value = 'test-api-key-123';
      
      // Mock lifeboard validation to return success
      mockLifeboard.settings.save.mockResolvedValue({
        success: true,
        validated: true
      });

      // Act: Attempt to save settings
      await settingsInstance.saveSettings();

      // Assert: Should call validation service
      expect(mockLifeboard.settings.save).toHaveBeenCalledWith(
        'limitless',
        expect.objectContaining({
          apiKey: 'test-api-key-123'
        }),
        null,
        { validateOnly: true }
      );
    });

    test('should handle lifeboard service unavailable', async () => {
      // Arrange: Remove lifeboard global to simulate service unavailable
      delete global.lifeboard;
      delete window.parent.lifeboard;
      
      const apiKeyInput = document.getElementById('apiKey');
      apiKeyInput.value = 'test-api-key-123';

      // Act: Attempt to save settings
      await settingsInstance.saveSettings();

      // Assert: Should show validation error
      expect(global.console.log).toHaveBeenCalledWith(
        expect.stringContaining('🚨 CRITICAL: lifeboard global not available')
      );
    });

    test('should handle validation service failure', async () => {
      // Arrange: Set up API key
      const apiKeyInput = document.getElementById('apiKey');
      apiKeyInput.value = 'invalid-key';
      
      // Mock lifeboard validation to return failure
      mockLifeboard.settings.save.mockResolvedValue({
        success: false,
        error: 'Invalid API key format'
      });

      // Act: Attempt to save settings
      await settingsInstance.saveSettings();

      // Assert: Should show validation error
      expect(global.console.log).toHaveBeenCalledWith(
        expect.stringContaining('🚨 DEBUG: Validation failed, NOT saving settings')
      );
      
      // Should not proceed to save
      expect(mockLifeboard.settings.save).toHaveBeenCalledTimes(1); // Only validation call
    });
  });

  describe('Form Data Collection', () => {
    test('should correctly identify empty API key', () => {
      // Arrange: Set up empty API key with various empty states
      const testCases = [
        { value: '', expected: '' },
        { value: '   ', expected: '' },
        { value: '\t\n ', expected: '' },
        { value: 'valid-key', expected: 'valid-key' }
      ];

      testCases.forEach(({ value, expected }) => {
        const apiKeyInput = document.getElementById('apiKey');
        apiKeyInput.value = value;

        // Act: Collect form data
        const formData = settingsInstance.collectFormData();

        // Assert: Should properly handle empty/whitespace values
        expect(formData.apiKey).toBe(expected);
        expect(!!formData.apiKey).toBe(expected !== '');
      });
    });
  });
});