<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real API Test - Limitless Plugin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ccc; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .debug-log { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; font-size: 12px; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 400px; }
        .result { padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .api-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .loading { color: #007bff; }
    </style>
</head>
<body>
    <h1>Real API Test - Limitless Plugin Validation</h1>
    
    <div class="api-info">
        <h3>🔑 Plugin Flow Simulation</h3>
        <p>This test simulates the complete plugin validation flow including IPC communication.</p>
        <p><strong>Note:</strong> Direct API calls fail due to CORS, but we can simulate the complete plugin workflow.</p>
    </div>
    
    <div class="test-section">
        <h2>🧪 Test Your Real API Key</h2>
        
        <div>
            <label for="realApiKey">Your Limitless AI API Key:</label><br>
            <input type="password" id="realApiKey" placeholder="Enter your real Limitless AI API key (UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)">
            <button onclick="togglePasswordVisibility()">👁️ Show/Hide</button>
            <br><br>
            <button onclick="testCompletePluginFlow()" id="testBtn">🔍 Test Complete Plugin Flow</button>
            <button onclick="testInvalidKey()">❌ Test Known Invalid Key</button>
            <button onclick="testIPCFlow()">📡 Test IPC Communication Flow</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📋 Debug Log</h2>
        <div id="debugLog"></div>
    </div>

    <script>
        let testResults = [];

        // Simulate the complete plugin validation flow
        async function simulatePluginValidationFlow(apiKey) {
            log(`🔍 SIMULATING COMPLETE PLUGIN FLOW for key: ${apiKey.substring(0, 8)}...`);

            // Step 1: Settings UI calls lifeboard.settings.save with validateOnly: true
            log('📱 Step 1: Settings UI → lifeboard.settings.save(validateOnly: true)');
            log('🔍 Simulating: await lifeboard.settings.save("limitless", formData, null, { validateOnly: true })');

            // Step 2: IPC layer receives the call
            log('📡 Step 2: IPC Handler receives settings:save request');
            log('🔍 IPC Handler: Checking validateOnly flag = true');

            // Step 3: Plugin Manager validation
            log('⚙️ Step 3: Plugin Manager → savePluginSettings(validateOnly=true)');
            log('🔍 Plugin Manager: Calling plugin.validateSettings()');

            // Step 4: Limitless Plugin validateSettings
            log('🔌 Step 4: Limitless Plugin → validateSettings()');
            log('🔍 Plugin: Calling LimitlessAPI.validateAPIKey()');

            // Step 5: LimitlessAPI validation (simulate based on key characteristics)
            log('🌐 Step 5: LimitlessAPI → validateAPIKey()');
            const apiValidationResult = await simulateAPIValidation(apiKey);

            // Step 6: Results propagate back
            log('📤 Step 6: Results propagating back through the chain');
            log('🔍 LimitlessAPI → Plugin → Plugin Manager → IPC → Settings UI');

            return apiValidationResult;
        }

        // Simulate API validation - REAL validation requires HTTP call to Limitless API
        async function simulateAPIValidation(apiKey) {
            log(`🔍 Simulating LimitlessAPI.validateAPIKey() for: ${apiKey.substring(0, 8)}...`);

            try {
                if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
                    log('❌ API validation: Empty or invalid format');
                    return {
                        success: false,
                        error: 'API key is required and must be a non-empty string'
                    };
                }

                // Simulate network delay
                await new Promise(resolve => setTimeout(resolve, 1000));

                const trimmedKey = apiKey.trim();

                // Only check for obviously test/fake keys
                if (trimmedKey.includes('invalid') ||
                    trimmedKey.includes('test') ||
                    trimmedKey.includes('fake') ||
                    trimmedKey === 'your-api-key-here') {
                    log('❌ API validation: Key contains test/invalid markers');
                    return {
                        success: false,
                        error: 'API not valid',
                        details: 'Test or invalid key detected'
                    };
                }

                // For real keys, we cannot determine validity without actual HTTP call
                log('⚠️ SIMULATION LIMITATION: Cannot validate real API keys without HTTP call');
                log('🌐 Real validation requires: HTTP request to Limitless API endpoint');
                log('🔑 Your key format appears to be: UUID-style (36 characters with dashes)');
                log('📡 In real plugin: This would make actual HTTP request to validate');

                // Since we can't make the real HTTP call, we'll indicate this limitation
                return {
                    success: null, // null indicates we can't determine without real HTTP call
                    error: 'Cannot simulate real validation - requires actual HTTP request',
                    details: 'Real plugin would make HTTP call to Limitless API to validate this key',
                    requiresRealValidation: true
                };

            } catch (error) {
                log(`💥 API validation error:`, {
                    message: error.message,
                    name: error.name
                });

                return {
                    success: false,
                    error: 'API not valid',
                    details: `Validation error: ${error.message}`
                };
            }
        }

        async function testCompletePluginFlow() {
            const apiKey = document.getElementById('realApiKey').value;
            const testBtn = document.getElementById('testBtn');

            if (!apiKey || apiKey.trim().length === 0) {
                addResult('error', '❌ Please enter an API key to test');
                return;
            }

            clearResults();
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing Complete Flow...';

            log(`🧪 TESTING COMPLETE PLUGIN FLOW`);
            log(`🔑 API Key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);
            log(`📏 Key Length: ${apiKey.length} characters`);
            log(`🏷️ Key Format: UUID-style (${apiKey.length === 36 && apiKey.includes('-') ? 'Standard UUID format' : 'Custom format'})`);

            try {
                // Simulate the complete plugin validation flow
                const validationResult = await simulatePluginValidationFlow(apiKey);

                log('📋 FINAL VALIDATION RESULT:', validationResult);

                if (validationResult.success === true) {
                    addResult('success', '✅ VALIDATION SUCCESS: API key appears valid');
                    addResult('success', '✅ Plugin would proceed to save settings');
                    addResult('success', '✅ User would see: "Settings saved successfully!"');
                    log('🎉 Complete flow: SUCCESS - settings would be saved');
                } else if (validationResult.success === false) {
                    addResult('error', `❌ VALIDATION FAILED: ${validationResult.error}`);
                    addResult('success', '✅ CORRECT: Plugin would NOT save settings');
                    addResult('success', '✅ User should see: "Invalid API key" error');
                    if (validationResult.details) {
                        addResult('error', `📝 Details: ${validationResult.details}`);
                    }
                    log('🔒 Complete flow: VALIDATION FAILED - settings would NOT be saved');
                } else {
                    // validationResult.success === null (cannot determine)
                    addResult('warning', '⚠️ SIMULATION LIMITATION: Cannot validate without real HTTP call');
                    addResult('warning', '🌐 Real plugin would make HTTP request to Limitless API');
                    addResult('warning', '🔍 Need to test in actual plugin to see real validation result');
                    if (validationResult.details) {
                        addResult('warning', `📝 ${validationResult.details}`);
                    }
                    log('⚠️ Complete flow: CANNOT SIMULATE - requires real HTTP validation');
                }

            } catch (error) {
                addResult('error', `❌ Flow test error: ${error.message}`);
                log(`💥 Flow test error: ${error.message}`);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🔍 Test Complete Plugin Flow';
            }
        }

        async function testIPCFlow() {
            log('📡 TESTING IPC COMMUNICATION FLOW');
            log('🔍 This simulates the IPC calls that happen in the real plugin');

            const apiKey = document.getElementById('realApiKey').value || 'test-key-for-ipc';

            // Simulate IPC call sequence
            log('1️⃣ Settings UI: Preparing IPC call');
            log('   → lifeboard.settings.save("limitless", formData, null, { validateOnly: true })');

            log('2️⃣ Preload Script: Forwarding to IPC');
            log('   → ipcRenderer.invoke("settings:save", "limitless", settings, schema, options)');

            log('3️⃣ IPC Handler: Processing request');
            log('   → PluginIpcHandlers.setupSettingsHandlers()');
            log('   → validateOnly = options?.validateOnly || false');
            log(`   → validateOnly = true (detected)`);

            log('4️⃣ Plugin Manager: Validation mode');
            log('   → pluginManager.savePluginSettings(pluginId, settings, true)');
            log('   → Calling plugin.validateSettings(settings)');

            log('5️⃣ Limitless Plugin: validateSettings()');
            log('   → this.limitlessAPI.validateAPIKey(settings.apiKey)');

            log('6️⃣ LimitlessAPI: HTTP validation');
            log('   → Making HTTP request to Limitless API');
            log('   → This is where the real validation happens');

            // Simulate the response chain
            const mockValidationResult = apiKey.includes('invalid') ?
                { success: false, error: 'API not valid' } :
                { success: true };

            log('7️⃣ Response Chain: Results flowing back');
            log('   → LimitlessAPI → Plugin → Plugin Manager → IPC → Preload → Settings UI');
            log('   → Final result:', mockValidationResult);

            if (mockValidationResult.success) {
                addResult('success', '📡 IPC Flow: Would return validation success');
                addResult('success', '📡 Settings UI: Would proceed to save');
            } else {
                addResult('error', '📡 IPC Flow: Would return validation failure');
                addResult('success', '📡 Settings UI: Would show error, NOT save');
            }
        }

        async function testInvalidKey() {
            // Test with a known invalid key format
            const invalidKey = 'invalid-test-key-12345';
            document.getElementById('realApiKey').value = invalidKey;

            clearResults();
            log('🧪 Testing with known invalid key for comparison');

            try {
                const validationResult = await simulatePluginValidationFlow(invalidKey);

                if (!validationResult.success) {
                    addResult('success', '✅ CORRECT: Known invalid key properly rejected');
                    addResult('success', '✅ This demonstrates proper validation behavior');
                    addResult('success', '✅ Settings would NOT be saved');
                } else {
                    addResult('error', '❌ UNEXPECTED: Invalid key was accepted');
                    addResult('error', '❌ This would be the bug!');
                }

            } catch (error) {
                addResult('error', `❌ Test error: ${error.message}`);
            }
        }

        function togglePasswordVisibility() {
            const input = document.getElementById('realApiKey');
            input.type = input.type === 'password' ? 'text' : 'password';
        }

        function addResult(type, message) {
            testResults.push({ type, message });
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="result ${result.type}">${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
            document.getElementById('debugLog').innerHTML = '';
        }

        function log(message, data = null) {
            const logDiv = document.getElementById('debugLog');
            const entry = document.createElement('div');
            entry.className = 'debug-log';
            
            const timestamp = new Date().toLocaleTimeString();
            let content = `[${timestamp}] ${message}`;
            
            if (data && typeof data === 'object') {
                content += '\n' + JSON.stringify(data, null, 2);
            } else if (data) {
                content += ' ' + data;
            }
            
            entry.textContent = content;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Auto-run instructions
        window.onload = () => {
            log('🔍 Plugin Flow Simulation Test loaded');
            log('📝 Instructions:');
            log('1. Enter your real Limitless AI API key (the one causing the bug)');
            log('2. Click "Test Complete Plugin Flow" to simulate the full validation process');
            log('3. Click "Test IPC Communication Flow" to see the IPC call sequence');
            log('4. Compare results with what you see in the actual plugin');
            log('');
            log('🎯 Goal: Determine if validation should succeed or fail for your key');
            log('🐛 Bug: If plugin saves settings when this test shows validation should fail');
        };
    </script>
</body>
</html>
