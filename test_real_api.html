<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real API Test - Limitless Plugin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ccc; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .debug-log { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; font-size: 12px; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 400px; }
        .result { padding: 15px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .api-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .loading { color: #007bff; }
    </style>
</head>
<body>
    <h1>Real API Test - Limitless Plugin Validation</h1>
    
    <div class="api-info">
        <h3>🔑 Real API Testing</h3>
        <p>This test makes actual HTTP requests to the Limitless AI API to validate your API key.</p>
        <p><strong>Safe:</strong> Only validation requests are made - no data is stored or transmitted beyond validation.</p>
    </div>
    
    <div class="test-section">
        <h2>🧪 Test Your Real API Key</h2>
        
        <div>
            <label for="realApiKey">Your Limitless AI API Key:</label><br>
            <input type="password" id="realApiKey" placeholder="Enter your real Limitless AI API key (e.g., lm-xxx or sk-xxx)">
            <button onclick="togglePasswordVisibility()">👁️ Show/Hide</button>
            <br><br>
            <button onclick="testRealAPIKey()" id="testBtn">🔍 Test Real API Key</button>
            <button onclick="testInvalidKey()">❌ Test Known Invalid Key</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>
        
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📋 Debug Log</h2>
        <div id="debugLog"></div>
    </div>

    <script>
        let testResults = [];

        // Real API validation function (same as in the plugin)
        async function validateRealAPIKey(apiKey) {
            log(`🔍 Starting REAL API validation for key: ${apiKey.substring(0, 8)}...`);
            
            try {
                if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
                    log('❌ API key format validation failed: empty or invalid format');
                    return {
                        success: false,
                        error: 'API key is required and must be a non-empty string'
                    };
                }

                // Make real HTTP request to Limitless AI API
                const url = 'https://api.limitlessai.com/v1/validate';
                log(`🌐 Making real HTTP request to: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-API-Key': apiKey.trim(),
                        'Content-Type': 'application/json'
                    }
                });

                log(`📡 HTTP Response received:`, {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ API validation successful:', data);
                    return {
                        success: true,
                        data: data
                    };
                } else {
                    const errorText = await response.text();
                    log(`❌ API validation failed:`, {
                        status: response.status,
                        statusText: response.statusText,
                        error: errorText
                    });

                    return {
                        success: false,
                        error: 'API not valid',
                        details: errorText,
                        httpStatus: response.status
                    };
                }
            } catch (error) {
                log(`💥 Network/API error:`, {
                    message: error.message,
                    name: error.name,
                    stack: error.stack
                });
                
                return {
                    success: false,
                    error: 'API not valid',
                    details: `Network error: ${error.message}`
                };
            }
        }

        async function testRealAPIKey() {
            const apiKey = document.getElementById('realApiKey').value;
            const testBtn = document.getElementById('testBtn');
            
            if (!apiKey || apiKey.trim().length === 0) {
                addResult('error', '❌ Please enter an API key to test');
                return;
            }
            
            clearResults();
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            
            log(`🧪 Testing REAL API key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);
            
            try {
                const validationResult = await validateRealAPIKey(apiKey);
                
                if (validationResult.success) {
                    addResult('success', '✅ SUCCESS: API key is VALID');
                    addResult('success', '✅ This key would allow settings to be saved');
                    log('🎉 Real API validation successful - key is valid');
                } else {
                    addResult('error', `❌ VALIDATION FAILED: ${validationResult.error}`);
                    addResult('success', '✅ CORRECT BEHAVIOR: Invalid key rejected, settings would NOT be saved');
                    if (validationResult.details) {
                        addResult('error', `Details: ${validationResult.details}`);
                    }
                    log('🔒 Real API validation failed - key is invalid (this is correct behavior)');
                }
                
            } catch (error) {
                addResult('error', `❌ Test error: ${error.message}`);
                log(`💥 Test error: ${error.message}`);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🔍 Test Real API Key';
            }
        }

        async function testInvalidKey() {
            // Test with a known invalid key format
            const invalidKey = 'invalid-test-key-12345';
            document.getElementById('realApiKey').value = invalidKey;
            
            clearResults();
            log('🧪 Testing with known invalid key for comparison');
            
            try {
                const validationResult = await validateRealAPIKey(invalidKey);
                
                if (!validationResult.success) {
                    addResult('success', '✅ CORRECT: Known invalid key properly rejected');
                    addResult('success', '✅ This demonstrates proper validation behavior');
                } else {
                    addResult('error', '❌ UNEXPECTED: Invalid key was accepted');
                }
                
            } catch (error) {
                addResult('error', `❌ Test error: ${error.message}`);
            }
        }

        function togglePasswordVisibility() {
            const input = document.getElementById('realApiKey');
            input.type = input.type === 'password' ? 'text' : 'password';
        }

        function addResult(type, message) {
            testResults.push({ type, message });
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="result ${result.type}">${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
            document.getElementById('debugLog').innerHTML = '';
        }

        function log(message, data = null) {
            const logDiv = document.getElementById('debugLog');
            const entry = document.createElement('div');
            entry.className = 'debug-log';
            
            const timestamp = new Date().toLocaleTimeString();
            let content = `[${timestamp}] ${message}`;
            
            if (data && typeof data === 'object') {
                content += '\n' + JSON.stringify(data, null, 2);
            } else if (data) {
                content += ' ' + data;
            }
            
            entry.textContent = content;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Auto-run instructions
        window.onload = () => {
            log('🔍 Real API test loaded');
            log('📝 Instructions:');
            log('1. Enter your real Limitless AI API key');
            log('2. Click "Test Real API Key" to validate it');
            log('3. This will make actual HTTP requests to validate the key');
            log('4. Compare results with what you see in the plugin');
        };
    </script>
</body>
</html>
